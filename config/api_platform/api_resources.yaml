resources:
  App\Dto\CarwebResponseDto:
    operations:
      ApiPlatform\Metadata\Get:
        uriTemplate: /test-harness/carweb-get-vehicle-by-vrm/{vrm}
        uriVariables: ['vrm']
        provider: App\StateProvider\CarwebGetVehicleByVRMStateProvider
        security: 'is_granted("TEST_HARNESS_GET_ITEM", object)'
  App\Dto\CarDetailsOutput:
    operations:
      ApiPlatform\Metadata\Get:
        uriTemplate: /car-details/{vrm}
        uriVariables: ['vrm']
        provider: App\StateProvider\CarDetailsStateProvider
  App\Entity\VehicleData:
    operations:
      ApiPlatform\Metadata\GetCollection:
        security: 'is_granted("VEHICLE_DATA_GET_COLLECTION", object)'
        uriTemplate: /vehicle-data
      get_vehicle_data_item:
        class: ApiPlatform\Metadata\Get
        status: 404
        controller: ApiPlatform\Action\NotFoundAction
        read: false
  App\Entity\PriceList:
    operations:
      ApiPlatform\Metadata\GetCollection:
        security: 'is_granted("PRICE_LIST_GET_COLLECTION", object)'
        uriTemplate: /price-lists
      ApiPlatform\Metadata\Get:
        method: GET
        uriTemplate: /price-lists/{id}
        security: 'is_granted("PRICE_LIST_GET_ITEM", object)'
    normalizationContext:
      groups: ['indespension_price_list.read']
    denormalizationContext:
      groups: ['indespension_price_list.write']
  App\Dto\ProductInformation:
    operations:
      product_information:
        class: ApiPlatform\Metadata\Get
        method: GET
        uriTemplate: /product-information
        output: App\Dto\ProductInformation
        provider: App\StateProvider\ProductInformationStateProvider
        normalizationContext:
          jsonld_has_context: true
          groups: ['product_information.read']
  App\Dto\CategoryInformation:
    operations:
      get_category_information:
        class: ApiPlatform\Metadata\GetCollection
        method: GET
        uriTemplate: /category-information
        provider: App\StateProvider\CategoryInformationStateProvider
        normalizationContext:
          jsonld_has_context: true
          groups: ['category_information.read', 'category.read']
  App\Dto\CarMakeModelDetailsDto:
    operations:
      ApiPlatform\Metadata\Get:
        uriTemplate: /car-make-model-search
        provider: App\StateProvider\CarMakeModelSearchStateProvider
  App\Dto\TowbarCompatibilityOutput:
    operations:
      ApiPlatform\Metadata\Get:
        method: GET
        uriTemplate: /towbars/compatibility/{kType}
        provider: App\State\Provider\TowbarCompatibilityProvider
        normalizationContext:
          jsonld_has_context: true
          groups: [storefront.product.slug.read]
      towbar_compatibility_item:
        class: ApiPlatform\Metadata\Get
        status: 404
        controller: ApiPlatform\Action\NotFoundAction
        read: false
  Peracto\Component\Location\Model\Location:
    operations:
      get_closed_location_by_postcode:
        class: ApiPlatform\Metadata\Get
        uriTemplate: /locations/closest/{postcode}
        uriVariables: ['postcode']
        provider: App\StateProvider\LocationClosestPostcodeStateProvider
        security: 'is_granted("LOCATION_GET_ITEM_CLOSEST_POSTCODE", object)'
