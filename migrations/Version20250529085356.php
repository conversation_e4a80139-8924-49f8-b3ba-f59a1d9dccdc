<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250529085356 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename refId column to kType in vehicle_data table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE vehicle_data CHANGE ref_id k_type INT NOT NULL');

        $this->addSql('DROP INDEX idx_ref_id ON vehicle_data');
        $this->addSql('DROP INDEX idx_vehicle ON vehicle_data');

        $this->addSql('CREATE INDEX idx_k_type ON vehicle_data (k_type)');
        $this->addSql('CREATE INDEX idx_vehicle ON vehicle_data (k_type, marque, model_range, variant)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE vehicle_data CHANGE k_type ref_id INT NOT NULL');

        $this->addSql('DROP INDEX idx_k_type ON vehicle_data');
        $this->addSql('DROP INDEX idx_vehicle ON vehicle_data');

        $this->addSql('CREATE INDEX idx_ref_id ON vehicle_data (ref_id)');
        $this->addSql('CREATE INDEX idx_vehicle ON vehicle_data (ref_id, marque, model_range, variant)');
    }
}
