<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250529090241 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename refId column to kType in store_products_ref_compatibility table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE store_products_ref_compatibility CHANGE ref_id k_type INT NOT NULL');

        $this->addSql('DROP INDEX idx_ref_id_product_id ON store_products_ref_compatibility');

        $this->addSql('CREATE UNIQUE INDEX idx_k_type_product_id ON store_products_ref_compatibility (k_type, product_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE store_products_ref_compatibility CHANGE k_type ref_id INT NOT NULL');

        $this->addSql('DROP INDEX idx_k_type_product_id ON store_products_ref_compatibility');

        $this->addSql('CREATE UNIQUE INDEX idx_ref_id_product_id ON store_products_ref_compatibility (ref_id, product_id)');
    }
}
