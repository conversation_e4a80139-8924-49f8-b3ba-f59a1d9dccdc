<?php

declare(strict_types=1);

namespace App\Component\Carweb\Api\GetVehicleByVRM\Response;

use App\Component\Carweb\Api\PropertyHelper;
use App\Component\Carweb\Response\CarwebResponse;
use App\Component\Carweb\Response\CarwebResponseInterface;
use Carbon\Carbon;

final class GetVehicleByVRMResponse extends CarwebResponse implements GetVehicleByVRMResponseInterface
{
    private ?bool $isSuccess = null;

    private ?string $errorCode = null;

    private ?string $errorDescription = null;

    public static function create(CarwebResponseInterface $response): self
    {
        $instance = new self($response->getResponseData(), $response->getRequest());

        $errorField = $response->getResponseData()['DataArea']['Error']['Details'] ?? null;

        if (isset($errorField)) {
            $instance->isSuccess = false;

            $instance->errorCode = (string) $errorField['ErrorCode'];
            $instance->errorDescription = (string) $errorField['ErrorDescription'];

            return $instance;
        }

        $instance->isSuccess = true;

        return $instance;
    }

    public function isSuccess(): ?bool
    {
        return $this->isSuccess;
    }

    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    public function getErrorDescription(): ?string
    {
        return $this->errorDescription;
    }

    public function getVehicleDetails(): ?array
    {
        if (!$this->isSuccess()) {
            return null;
        }

        $vehicle = $this->responseData['DataArea']['Vehicles']['Vehicle'];

        $dateFirstRegistered = $vehicle[PropertyHelper::CARWEB_FIELD_DATE_FIRST_REGISTERED];

        return array_map(fn($value) => !empty($value) ? $value : '', [
            'make' => $vehicle[PropertyHelper::CARWEB_FIELD_MAKE],
            'model' => $vehicle[PropertyHelper::CARWEB_FIELD_MODEL],
            'year' => Carbon::createFromFormat(PropertyHelper::CARWEB_DATE_FORMAT, $dateFirstRegistered)->year,
            'dateFirstRegistered' => $dateFirstRegistered,
            'modelSeries' => $vehicle[PropertyHelper::CARWEB_FIELD_MODEL_SERIES],
            'bodyStyle' => $vehicle[PropertyHelper::CARWEB_FIELD_BODY_STYLE],
            'description' => $vehicle[PropertyHelper::CARWEB_FIELD_DESCRIPTION],
            'kType' => $vehicle[PropertyHelper::CARWEB_FIELD_K_TYPE]
        ]);
    }
}
