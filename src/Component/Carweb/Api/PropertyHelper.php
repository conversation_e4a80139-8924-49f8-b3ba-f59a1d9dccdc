<?php

declare(strict_types=1);

namespace App\Component\Carweb\Api;

final readonly class PropertyHelper
{
    public const CARWEB_DATE_FORMAT = 'Y-m-d';

    public const CARWEB_FIELD_MAKE = 'DVLA_Make';
    public const CARWEB_FIELD_MODEL = 'DVLA_Model';
    public const CARWEB_FIELD_DATE_FIRST_REGISTERED = 'DateFirstRegistered';
    public const CARWEB_FIELD_MODEL_SERIES = 'ModelSeries';
    public const CARWEB_FIELD_BODY_STYLE = 'BodyStyle';
    public const CARWEB_FIELD_DESCRIPTION = 'DVLA_Vehicle_Description';
    public const CARWEB_FIELD_REF_ID = 'RefID';
    public const CARWEB_FIELD_K_TYPE = 'KType';

    public const CARWEB_GET_VEHICLE_BY_VRM_RESPONSE_REQUIRED_FIELDS = [
        self::CARWEB_FIELD_MAKE,
        self::CARWEB_FIELD_MODEL,
        self::CARWEB_FIELD_DATE_FIRST_REGISTERED,
        self::CARWEB_FIELD_BODY_STYLE,
        self::CARWEB_FIELD_DESCRIPTION,
        self::CARWEB_FIELD_K_TYPE
    ];
}
