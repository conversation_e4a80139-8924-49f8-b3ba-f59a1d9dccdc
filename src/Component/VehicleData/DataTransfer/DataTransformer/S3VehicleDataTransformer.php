<?php

declare(strict_types=1);

namespace App\Component\VehicleData\DataTransfer\DataTransformer;

use App\Component\VehicleData\VehicleDataServiceInterface;
use Peracto\Component\DataTransfer\DataTransformer\DataTransformerInterface;

readonly class S3VehicleDataTransformer implements DataTransformerInterface
{
    public function __construct(
        private VehicleDataTransformer $vehicleDataTransformer
    ) {
    }

    public function transform(array $data): array
    {
        return $data;
    }

    public function reverseTransform(array $data): array
    {
        return $this->vehicleDataTransformer->reverseTransform([
            'k_type' => $data[VehicleDataServiceInterface::COLUMN_K_TYPE],
            'marque' => $data[VehicleDataServiceInterface::COLUMN_MARQUE],
            'model_range' => $data[VehicleDataServiceInterface::COLUMN_MODEL_RANGE],
            'body_type' => $data[VehicleDataServiceInterface::COLUMN_BODY_TYPE],
            'intro_year' => $data[VehicleDataServiceInterface::COLUMN_INTRO_YEAR],
            'end_year' => $data[VehicleDataServiceInterface::COLUMN_END_YEAR],
            'variant' => $data[VehicleDataServiceInterface::COLUMN_VARIANT],
        ]);
    }
}
