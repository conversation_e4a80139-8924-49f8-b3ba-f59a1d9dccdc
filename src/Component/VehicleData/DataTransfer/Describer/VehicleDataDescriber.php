<?php

declare(strict_types=1);

namespace App\Component\VehicleData\DataTransfer\Describer;

use App\Entity\VehicleData;
use Peracto\Component\DataTransfer\Describer\DescriberInterface;
use Peracto\Component\DataTransfer\Describer\DescriberPropertyTrait;

class VehicleDataDescriber implements DescriberInterface
{
    use DescriberPropertyTrait;

    private const IMPORT_PROPERTIES = [
        ['key' => 'k_type', 'required' => true],
        ['key' => 'marque', 'required' => true],
        ['key' => 'model_range', 'required' => true],
        ['key' => 'body_type', 'required' => true],
        ['key' => 'intro_year', 'required' => true],
        ['key' => 'end_year', 'required' => false],
        ['key' => 'variant', 'required' => true],
    ];

    public function getEntityClasses(): array
    {
        return [VehicleData::class];
    }

    public function getProperties(): array
    {
        return self::IMPORT_PROPERTIES;
    }
}
