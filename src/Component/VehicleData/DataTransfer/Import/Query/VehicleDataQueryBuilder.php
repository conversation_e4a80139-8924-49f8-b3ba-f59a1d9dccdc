<?php

declare(strict_types=1);

namespace App\Component\VehicleData\DataTransfer\Import\Query;

use App\Entity\VehicleData;
use Peracto\Component\DataTransfer\Import\Query\AbstractQueryBuilder;
use Peracto\Component\DataTransfer\Import\Query\QueryBuilderInterface;

class VehicleDataQueryBuilder extends AbstractQueryBuilder implements QueryBuilderInterface
{
    /**
     * @param array{
     *     k_type: int,
     *     marque: string,
     *     model_range: string,
     *     body_type: string,
     *     intro_year: int,
     *     end_year: int,
     *     variant: string,
     * } $data
     */
    public function getQueries(array $data, array $options = []): iterable
    {
        return [
            $this->getInsertQuery(
                VehicleData::class,
                array_merge(
                    $data,
                    $options['default_values'] ?? []
                )
            )
        ];
    }
}
