<?php

declare(strict_types=1);

namespace App\Component\VehicleData;

use App\Component\File\Exception\ImportVehicleDataException;
use App\Component\File\FileStorageServiceInterface;
use Carbon\Carbon;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SplFileObject;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\Messenger\MessageBusInterface;

class VehicleDataService implements VehicleDataServiceInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    private const REQUIRED_COLUMNS = [
        self::COLUMN_K_TYPE,
        self::COLUMN_VEHICLE_CATEGORY_DESCRIPTION,
        self::COLUMN_MARQUE,
        self::COLUMN_MODEL_RANGE,
        self::COLUMN_INTRO_YEAR,
        self::COLUMN_END_YEAR,
        self::COLUMN_DOORS,
        self::COLUMN_BODY_TYPE,
        self::COLUMN_RANGE_SERIES,
        self::COLUMN_VARIANT,
    ];

    private const FILE_FRIENDLY_DATE_FORMAT = 'Y_m_d_His';
    private const VEHICLE_DATA_FILE = 'vehicle_data/New_Codes.csv';
    private const PROCESSED_VEHICLE_DATA_FILE = 'vehicle_data/processed/processed_vehicle_data_%s.csv';

    public function __construct(
        private readonly FileStorageServiceInterface $fileStorageService,
    ) {
    }

    public function getVehicleData(): array
    {
        $csv = $this->getCsvFile();
        $csvData = iterator_to_array($csv);
        $headers = array_map(static fn($header) => strtoupper(trim($header)), array_shift($csvData));

        $this->checkMissingColumns($headers);

        return [
            'headers' => $headers,
            'data' => $csvData,
        ];
    }

    private function checkMissingColumns(array $headers): void
    {
        $missingColumns = array_diff(self::REQUIRED_COLUMNS, $headers);
        if ($missingColumns) {
            $message = sprintf('<error>Missing the following columns: %s</error>', implode(',', $missingColumns));
            $this->logger->alert($message);

            throw new ImportVehicleDataException($message);
        }
    }

    private function getCsvFile(): SplFileObject
    {
        try {
            $this->fileStorageService->downloadFile(self::VEHICLE_DATA_FILE);
            $this->fileStorageService->moveFile(
                self::VEHICLE_DATA_FILE,
                sprintf(self::PROCESSED_VEHICLE_DATA_FILE, Carbon::now()->format(self::FILE_FRIENDLY_DATE_FORMAT))
            );

            $file = new File(
                $this->fileStorageService->getDownloadedFileFullPath(self::VEHICLE_DATA_FILE)
            );

            $csv = $file->openFile('r');
            $csv->setFlags(
                SplFileObject::READ_CSV |
                SplFileObject::SKIP_EMPTY |
                SplFileObject::READ_AHEAD |
                SplFileObject::DROP_NEW_LINE
            );

            return $csv;
        } catch (\Throwable $exception) {
            $message = sprintf("<error>Unable to locate the source file. Error: %s</error>", $exception->getMessage());
            $this->logger->alert($message);

            throw new ImportVehicleDataException($message);
        }
    }
}
