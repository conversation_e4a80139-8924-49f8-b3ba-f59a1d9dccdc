<?php

declare(strict_types=1);

namespace App\Component\VehicleData;

interface VehicleDataServiceInterface
{
    public const COLUMN_K_TYPE = 'K TYPE';
    public const COLUMN_VEHICLE_CATEGORY_DESCRIPTION = 'VEHICLE CATEGORY DESCRIPTION';
    public const COLUMN_MARQUE = 'MARQUE';
    public const COLUMN_MODEL_RANGE = 'MODEL RANGE';
    public const COLUMN_INTRO_YEAR = 'INTRO YEAR';
    public const COLUMN_END_YEAR = 'END YEAR';
    public const COLUMN_DOORS = 'DOORS';
    public const COLUMN_BODY_TYPE = 'BODY TYPE';
    public const COLUMN_RANGE_SERIES = 'RANGE SERIES';
    public const COLUMN_VARIANT = 'VARIANT';

    /**
     * @return array{
     *     headers: array<string>,
     *     data: array<array<string>>
     * }
     */
    public function getVehicleData(): array;
}
