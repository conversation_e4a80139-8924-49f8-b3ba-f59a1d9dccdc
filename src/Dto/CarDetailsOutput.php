<?php

declare(strict_types=1);

namespace App\Dto;

readonly class CarDetailsOutput
{
    public function __construct(
        private string $make,
        private string $model,
        private int $year,
        private string $kType,
    ) {
    }

    public function getMake(): string
    {
        return $this->make;
    }

    public function getModel(): string
    {
        return $this->model;
    }

    public function getYear(): int
    {
        return $this->year;
    }

    public function getKType(): string
    {
        return $this->kType;
    }
}
