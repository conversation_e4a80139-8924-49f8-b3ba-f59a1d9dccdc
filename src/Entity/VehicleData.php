<?php

declare(strict_types=1);

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity()]
#[ORM\Table(name: "vehicle_data")]
#[ORM\Index(fields: ["kType"], name: "idx_k_type")]
#[ORM\Index(fields: ["marque"], name: "idx_marque")]
#[ORM\Index(fields: ["marque", "modelRange"], name: "idx_marque_model_range")]
#[ORM\Index(fields: ["kType", "marque", "modelRange", "variant"], name: "idx_vehicle")]
class VehicleData implements VehicleDataInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: "AUTO")]
    #[ORM\Column(type: "integer")]
    private int $id;

    #[ORM\Column(type: 'integer')]
    private int $kType;

    #[ORM\Column(type: 'string', length: 255)]
    private string $marque;

    #[ORM\Column(type: 'string', length: 255)]
    private string $modelRange;

    #[ORM\Column(type: 'integer')]
    private int $introYear;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $endYear;

    #[ORM\Column(type: 'string', length: 255)]
    private string $bodyType;

    #[ORM\Column(type: 'string', length: 255)]
    private string $variant;

    public function getId(): int
    {
        return $this->id;
    }

    public function getKType(): int
    {
        return $this->kType;
    }

    public function setKType(int $kType): static
    {
        $this->kType = $kType;

        return $this;
    }

    public function getMarque(): string
    {
        return $this->marque;
    }

    public function setMarque(string $marque): static
    {
        $this->marque = $marque;

        return $this;
    }

    public function getModelRange(): string
    {
        return $this->modelRange;
    }

    public function setModelRange(string $modelRange): static
    {
        $this->modelRange = $modelRange;

        return $this;
    }

    public function getBodyType(): string
    {
        return $this->bodyType;
    }

    public function setBodyType(string $bodyType): static
    {
        $this->bodyType = $bodyType;

        return $this;
    }

    public function getIntroYear(): int
    {
        return $this->introYear;
    }

    public function setIntroYear(int $introYear): static
    {
        $this->introYear = $introYear;

        return $this;
    }

    public function getEndYear(): ?int
    {
        return $this->endYear;
    }

    public function setEndYear(?int $endYear): static
    {
        $this->endYear = $endYear;

        return $this;
    }

    public function getVariant(): string
    {
        return $this->variant;
    }

    public function setVariant(string $variant): static
    {
        $this->variant = $variant;

        return $this;
    }
}
