<?php

declare(strict_types=1);

namespace App\Entity;

interface VehicleDataInterface
{
    public function getId(): int;
    public function getKType(): int;
    public function setKType(int $kType): static;
    public function getMarque(): string;
    public function setMarque(string $marque): static;
    public function getModelRange(): string;
    public function setModelRange(string $modelRange): static;
    public function getBodyType(): string;
    public function setBodyType(string $bodyType): static;
    public function getIntroYear(): int;
    public function setIntroYear(int $introYear): static;
    public function getEndYear(): ?int;
    public function setEndYear(?int $endYear): static;
    public function getVariant(): string;
    public function setVariant(string $variant): static;
}
