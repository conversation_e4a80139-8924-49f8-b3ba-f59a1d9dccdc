<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Product;
use App\Entity\CategoryInterface;
use App\Entity\ProductInterface;
use App\Entity\ProductRefCompatibility;
use Doctrine\ORM\Query\Expr\Join;
use Peracto\Bundle\CatalogueBundle\Repository\ProductRepository as PeractoProductRepository;
use Peracto\Component\Attribute\Model\Attribute;
use Peracto\Component\Category\Model\CategoryProductLink;
use Peracto\Component\Product\Model\ProductAttribute;

class ProductRepository extends PeractoProductRepository implements ProductRepositoryInterface
{
    public function getByProductMenuItemRelation(int $id): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        $expr = $qb->expr();

        return $qb->select('product')
            ->from(Product::class, 'product')
            ->innerJoin('product.productMenuItemRelations', 'pmr')
            ->innerJoin('pmr.menuItems', 'mi')
            ->andWhere($expr->eq('mi.id', ':id'))
            ->setParameter('id', $id)
            ->getQuery()
            ->getResult();
    }

    public function getLowestPricedProductForCategory(CategoryInterface $category): ?ProductInterface
    {
        $qb = $this->entityManager->createQueryBuilder();
        $expr = $qb->expr();

        $qb->select('p')
            ->from(ProductInterface::class, 'p')
            ->where($expr->eq('p.status', ':productStatus'))
            ->andWhere($expr->in('p.availability', ':productAvailability'))
            ->setParameter('productStatus', ProductInterface::STATUS_ACTIVE)
            ->setParameter('productAvailability', [
                ProductInterface::AVAILABILITY_PURCHASABLE,
                ProductInterface::AVAILABILITY_ENQUIRY_ONLY
            ]);

        if (CategoryInterface::CATEGORY_TYPE_DYNAMIC === $category->getType()) {
            $options = $category->getOptions();
            $productCategories = [];
            $operator = null;

            foreach ($options['attributes'] ?? [] as $attribute) {
                if ('product_category' !== $attribute['attribute']['code']) {
                    continue;
                }

                $operator = $attribute['operator'];

                foreach ($attribute['values'] ?? [] as $value) {
                    $productCategories[] = $value['value'];
                }
            }

            $qb->innerJoin(ProductAttribute::class, 'pa', Join::WITH, 'p = pa.product')
                ->innerJoin(Attribute::class, 'at', Join::WITH, 'pa.attribute = at')
                ->andWhere('at.code = :attributeCode')
                ->setParameter('attributeCode', 'product_category');

            $conditions = [];
            for ($i = 0; $i < count($productCategories); $i++) {
                $conditions[] = $expr->like('pa.value', ':category' . $i);
                $qb->setParameter('category' . $i, sprintf('%%%s%%', $productCategories[$i]));
            }

            if ('or' === $operator) {
                $qb->andWhere($expr->orX(...$conditions));
            } else {
                $qb->andWhere($expr->andX(...$conditions));
            }
        } else {
            $qb->innerJoin(CategoryProductLink::class, 'cpl', Join::WITH, 'p = cpl.product')
                ->andWhere($expr->eq('cpl.category', ':category'))
                ->setParameter('category', $category);
        }

        $qb->orderBy('p.price', 'ASC')
            ->setFirstResult(0)
            ->setMaxResults(1);

        $result = $qb->getQuery()->getResult();

        return !empty($result) ? $result[0] : null;
    }

    public function findAllForKType(int $kType): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        return $qb->select('p')
            ->from(ProductInterface::class, 'p')
            ->innerJoin(ProductRefCompatibility::class, 'prc', Join::WITH, 'p = prc.product')
            ->andWhere('prc.kType = :kType')
            ->setParameter('kType', $kType)
            ->getQuery()
            ->getResult();
    }
}
