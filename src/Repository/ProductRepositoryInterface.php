<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\CategoryInterface;
use App\Entity\ProductInterface;
use Peracto\Component\Product\Repository\ProductRepositoryInterface as PeractoProductRepositoryInterface;

interface ProductRepositoryInterface extends PeractoProductRepositoryInterface
{
    /**
     * @param int $id
     * @return ProductInterface[]
     */
    public function getByProductMenuItemRelation(int $id): array;

    public function getLowestPricedProductForCategory(CategoryInterface $category): ?ProductInterface;

    /**
     * @param int $kType
     * @return ProductInterface[]
     */
    public function findAllForKType(int $kType): array;
}
