<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\VehicleData;
use App\Entity\VehicleDataInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query;

class VehicleDataRepository implements VehicleDataRepositoryInterface
{
    public function __construct(
        protected EntityManagerInterface $entityManager
    ) {
    }

    public function getAllMakes(): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        $marques = $qb
            ->select('vd.marque')
            ->distinct()
            ->from(VehicleData::class, 'vd')
            ->orderBy('vd.marque', 'ASC')
            ->getQuery()
            ->getResult();

        return array_column($marques, 'marque');
    }

    public function getModelsForMake(string $make): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        $expr = $qb->expr();

        $models = $qb
            ->select('vd.modelRange')
            ->distinct()
            ->from(VehicleData::class, 'vd')
            ->where($expr->eq('vd.marque', ':make'))
            ->setParameter('make', $make)
            ->orderBy('vd.modelRange', 'ASC')
            ->getQuery()
            ->getResult();

        return array_column($models, 'modelRange');
    }

    public function getYearsForMakeModel(string $make, string $model): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        $expr = $qb->expr();

        $vehicleData = $qb
            ->select('vd.introYear')
            ->distinct()
            ->from(VehicleData::class, 'vd')
            ->where($expr->eq('vd.marque', ':make'))
            ->andWhere($expr->eq('vd.modelRange', ':model'))
            ->andWhere($expr->isNotNull('vd.introYear'))
            ->setParameter('make', $make)
            ->setParameter('model', $model)
            ->orderBy('vd.introYear', 'ASC')
            ->getQuery()
            ->getResult();

        return array_column($vehicleData, 'introYear');
    }

    public function getVehicleData(string $make, string $model, int $year): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        $expr = $qb->expr();

        return $qb
            ->select('vd')
            ->from(VehicleData::class, 'vd')
            ->where($expr->eq('vd.marque', ':make'))
            ->andWhere($expr->eq('vd.modelRange', ':model'))
            ->andWhere($expr->eq('vd.introYear', ':year'))
            ->setParameter('make', $make)
            ->setParameter('model', $model)
            ->setParameter('year', $year)
            ->orderBy('vd.variant', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function find(string $id): ?VehicleDataInterface
    {
        return $this->getRepository()->find($id);
    }

    /**
     * @inheritDoc
     */
    public function findAll(): array
    {
        return $this->getRepository()->findAll();
    }

    /**
     * @inheritDoc
     */
    public function findBy(array $criteria, ?array $orderBy = null, ?int $limit = null, ?int $offset = null): array
    {
        return $this->getRepository()->findBy($criteria, $orderBy, $limit, $offset);
    }

    public function findOneBy(array $criteria): ?VehicleDataInterface
    {
        return $this->getRepository()->findOneBy($criteria);
    }

    public function findOneByKType(string $kType, bool $includeRefresh = false): ?VehicleDataInterface
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select('am')
            ->from(VehicleDataInterface::class, 'am')
            ->where('am.kType = :kType')
            ->setParameter('kType', $kType)
            ->getQuery()
        ;

        if ($includeRefresh) {
            $query->setHint(Query::HINT_REFRESH, true);
        }

        $result = $query->getResult();

        return !empty($result) ? $result[0] : null;
    }

    public function save(VehicleDataInterface $entity): VehicleDataInterface
    {
        return $this->entityManager->transactional(function () use ($entity) {
            if (!$entity->getKType()) {
                $this->entityManager->persist($entity);
            }

            return $entity;
        });
    }

    public function remove(VehicleDataInterface $entity): void
    {
        $this->entityManager->transactional(function () use ($entity) {
            $this->entityManager->remove($entity);
        });
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function deleteAll(): void
    {
        $this
            ->entityManager
            ->createQueryBuilder()
            ->delete(VehicleData::class, 'vd')
            ->getQuery()
            ->execute();
    }

    protected function getRepository()
    {
        return $this->entityManager->getRepository(VehicleData::class);
    }
}
