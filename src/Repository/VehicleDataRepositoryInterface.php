<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\VehicleDataInterface;

interface VehicleDataRepositoryInterface
{
    public function find(string $id): ?VehicleDataInterface;
    public function findAll(): array;

    /**
     * @param array $criteria
     * @param array|null $orderBy
     * @param int|null $limit
     * @param int|null $offset
     * @return array
     */
    public function findBy(array $criteria, ?array $orderBy = null, ?int $limit = null, ?int $offset = null): array;
    public function findOneBy(array $criteria): ?VehicleDataInterface;
    public function findOneByKType(string $kType, bool $includeRefresh = false): ?VehicleDataInterface;
    public function save(VehicleDataInterface $entity): VehicleDataInterface;
    public function remove(VehicleDataInterface $entity): void;
    public function deleteAll(): void;

    /**
     * @return string[]
     */
    public function getAllMakes(): array;

    /**
     * @param string $make
     * @return string[]
     */
    public function getModelsForMake(string $make): array;

    /**
     * @param string $make
     * @param string $model
     *
     * @return int[]
     */
    public function getYearsForMakeModel(string $make, string $model): array;

    /**
     * @param string $make
     * @param string $model
     * @param int $year
     * @return VehicleDataInterface[]
     */
    public function getVehicleData(string $make, string $model, int $year): array;
}
