<?php

declare(strict_types=1);

namespace App\State\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Dto\StorefrontOutput;
use App\Dto\TowbarCompatibilityOutput;
use App\Repository\ProductRepositoryInterface;
use Peracto\Component\Product\Mapper\ProductMapperInterface;
use Peracto\Component\Product\Model\ProductInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TowbarCompatibilityProvider implements ProviderInterface
{
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly ProductMapperInterface $productMapper,
        private readonly Security $security
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): TowbarCompatibilityOutput
    {
        $kType = (int) $uriVariables['kType'];
        $products = $this->productRepository->findAllForKType($kType);

        if (empty($products)) {
            throw new NotFoundHttpException(sprintf('No compatible towbars found for K-Type: %d', $kType));
        }

        $context[ProductInterface::GET_STOREFRONT_OPERATION_NAME]['variant'] = null;
        $context[ProductInterface::GET_STOREFRONT_OPERATION_NAME]['user'] = $this->security->getUser();

        $outputs = [];

        foreach ($products as $product) {
            $outputs[] = $this->productMapper->map($product, new StorefrontOutput(), $context);
        }

        return new TowbarCompatibilityOutput($outputs);
    }
}
