<?php

declare(strict_types=1);

namespace App\StateProvider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Component\Carweb\Api\GetVehicleByVRM\GetVehicleByVRMServiceInterface;
use App\Component\Carweb\Exception\CarwebValidationException;
use App\Dto\CarDetailsOutput;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

readonly class CarDetailsStateProvider implements ProviderInterface
{
    public function __construct(
        private GetVehicleByVRMServiceInterface $vehicleVrmService
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): CarDetailsOutput
    {
        $vrm = $uriVariables['vrm'];

        try {
            $carDetailsResponse = $this->vehicleVrmService->getVehicleDetailsByVrm($vrm);
        } catch (CarwebValidationException $exception) {
            throw new BadRequestException($exception->getMessage());
        }

        if (!$carDetailsResponse->isSuccess()) {
            throw new BadRequestException(sprintf('Could not retrieve vehicle details for "%s".', $vrm));
        }

        $carDetails = $carDetailsResponse->getVehicleDetails();

        return new CarDetailsOutput($carDetails['make'], $carDetails['model'], $carDetails['year'], $carDetails['kType']);
    }
}
