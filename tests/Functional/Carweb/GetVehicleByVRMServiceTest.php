<?php

declare(strict_types=1);

namespace App\Tests\Functional\Carweb;

use App\Component\Carweb\Api\GetVehicleByVRM\GetVehicleByVRMService;
use App\Component\Carweb\MockCarwebHttpClient;
use App\Component\Carweb\Validator\CarwebRequestValidatorInterface;
use App\Component\Carweb\Validator\CarwebResponseValidatorInterface;
use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;

class GetVehicleByVRMServiceTest extends OptimizedJsonApiTestCase
{
    private ?GetVehicleByVRMService $sut;

    private ?MockCarwebHttpClient $carwebClient;
    private ?CarwebRequestValidatorInterface $requestValidator;
    private ?CarwebResponseValidatorInterface $responseValidator;

    protected function setUp(): void
    {
        $this->carwebClient = $this->getContainer()->get(MockCarwebHttpClient::class);
        $this->requestValidator = $this->getContainer()->get('app.component.carweb.validator.get_vehicle_by_vrm_request_validator');
        $this->responseValidator = $this->getContainer()->get('app.component.carweb.validator.get_vehicle_by_vrm_response_validator');

        $this->sut = new GetVehicleByVRMService(
            $this->carwebClient,
            $this->requestValidator,
            $this->responseValidator,
        );
    }

    public function test_service_returns_all_data_when_carweb_returns_all_car_details(): void
    {
        $this->carwebClient->setResponseFile(
            ['directory' => 'Carweb', 'filename' => 'get-vehicle-by-vrm-success-all-data.xml']
        );

        $expected = [
            'make' => 'FORD',
            'model' => 'FIESTA TITANIUM',
            'dateFirstRegistered' => '2015-07-07',
            'modelSeries' => 'MK7 FL (B299)',
            'bodyStyle' => '5 DOOR HATCHBACK',
            'description' => 'CARS (exc. Off-Road)',
            'year' => 2015,
            'kType' => '58967',
        ];

        $response = $this->sut->getVehicleDetailsByVrm('YW15OTE');

        self::assertTrue($response->isSuccess());
        self::assertEquals($expected, $response->getVehicleDetails());
    }

    public function test_service_returns_partial_data_when_carweb_returns_incomplete_car_details(): void
    {
        $this->carwebClient->setResponseFile(
            ['directory' => 'Carweb', 'filename' => 'get-vehicle-by-vrm-success-partial-data.xml']
        );

        $expected = [
            'make' => 'FORD',
            'model' => '',
            'dateFirstRegistered' => '2015-07-07',
            'modelSeries' => '',
            'bodyStyle' => '',
            'description' => 'CARS (exc. Off-Road)',
            'year' => 2015,
            'kType' => '58967',
        ];

        $response = $this->sut->getVehicleDetailsByVrm('YW15OTE');

        self::assertTrue($response->isSuccess());
        self::assertEquals($expected, $response->getVehicleDetails());
    }

    public function test_service_has_error_details_when_carweb_returns_no_vehicle(): void
    {
        $this->carwebClient->setResponseFile(
            ['directory' => 'Carweb', 'filename' => 'get-vehicle-by-vrm-failure-no-vehicle.xml']
        );

        $response = $this->sut->getVehicleDetailsByVrm('YW15OTE');

        self::assertFalse($response->isSuccess());
        self::assertEquals('1000', $response->getErrorCode());
        self::assertEquals('No Vehicles Returned', $response->getErrorDescription());
    }
}
