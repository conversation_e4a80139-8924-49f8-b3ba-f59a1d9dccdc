<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Repository\ProductRepositoryInterface;
use Peracto\Testing\TestCase\OptimizedKernelTestCase;

class ProductRepositoryTest extends OptimizedKernelTestCase
{
    /**
     * @dataProvider kTypeDataProvider
     */
    public function test_repository_returns_correct_products_for_k_type(int $kType, array $expectedSkus): void
    {
        $productRepository = self::getContainer()->get(ProductRepositoryInterface::class);

        $products = $productRepository->findAllForKType($kType);

        self::assertCount(count($expectedSkus), $products);

        foreach ($products as $product) {
            self::assertContains($product->getSku(), $expectedSkus);
        }
    }

    private static function kTypeDataProvider(): iterable
    {
        yield 'K-Type with 3 products' => [
            690202,
            ['PK123456', 'PK987654', 'PK987657'],
        ];

        yield 'K-Type with 1 product' => [
            701873,
            ['PK987658'],
        ];
    }
}
