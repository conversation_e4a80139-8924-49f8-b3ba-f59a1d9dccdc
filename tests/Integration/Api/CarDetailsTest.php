<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use App\Component\Carweb\MockCarwebHttpClient;
use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class CarDetailsTest extends OptimizedJsonApiTestCase
{
    private ?MockCarwebHttpClient $mockCarwebClient;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockCarwebClient = $this->getContainer()->get(MockCarwebHttpClient::class);
    }

    public function test_car_details_fields_are_returned(): void
    {
        $this->mockCarwebClient->setResponseFile(
            ['directory' => 'Carweb', 'filename' => 'get-vehicle-by-vrm-success-all-data.xml']
        );

        $response = $this->client->request('GET', '/car-details/EO65LWM');

        $this->assertResponseCode($response, Response::HTTP_OK);

        $this->assertJsonContains([
            'make' => 'FORD',
            'model' => 'FIESTA TITANIUM',
            'year' => 2015,
            'kType' => '58967',
        ]);
    }

    public function test_endpoint_400s_and_provides_a_message_when_vrm_is_not_found_in_carweb(): void
    {
        $this->mockCarwebClient->setResponseFile(
            ['directory' => 'Carweb', 'filename' => 'get-vehicle-by-vrm-failure-no-vehicle.xml']
        );

        $response = $this->client->request('GET', '/car-details/12345');

        $this->assertResponseCode($response, Response::HTTP_BAD_REQUEST);

        $this->assertJsonContains([
            'hydra:description' => 'Could not retrieve vehicle details for "12345".',
        ]);
    }

    public function test_endpoint_400s_and_provides_a_message_when_vrm_is_too_long(): void
    {
        $this->mockCarwebClient->setResponseFile(
            ['directory' => 'Carweb', 'filename' => 'get-vehicle-by-vrm-success-all-data.xml']
        );

        $response = $this->client->request('GET', '/car-details/ABCD12345678');

        $this->assertResponseCode($response, Response::HTTP_BAD_REQUEST);

        $this->assertJsonContains([
            'hydra:description' => 'VRM cannot be greater than 7 characters.',
        ]);
    }

    public function test_endpoint_400s_and_provides_a_message_when_vrm_invalid_syntax(): void
    {
        $this->mockCarwebClient->setResponseFile(
            ['directory' => 'Carweb', 'filename' => 'get-vehicle-by-vrm-success-all-data.xml']
        );

        $response = $this->client->request('GET', '/car-details/++++++');

        $this->assertResponseCode($response, Response::HTTP_BAD_REQUEST);

        $this->assertJsonContains([
            'hydra:description' => 'VRM must only contain alphanumeric characters.',
        ]);
    }
}
