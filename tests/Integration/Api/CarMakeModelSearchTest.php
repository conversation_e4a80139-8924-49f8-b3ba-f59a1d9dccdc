<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class CarMakeModelSearchTest extends OptimizedJsonApiTestCase
{
    /**
     * @dataProvider invalidSearchDataProvider
     */
    public function test_fields_are_empty_when_search_is_not_valid(array $queryParameters, array $expectedFieldsToBePopulated, array $expectedFieldsToBeEmpty): void
    {
        $queryString = !empty($queryParameters) ? '?' . http_build_query($queryParameters) : '';

        $response = $this->client->request('GET', '/car-make-model-search' . $queryString);
        $this->assertResponseCode($response, Response::HTTP_OK);

        $responseArray = $response->toArray();

        foreach ($expectedFieldsToBePopulated as $field) {
            self::assertNotEmpty($responseArray[$field]);
        }

        foreach ($expectedFieldsToBeEmpty as $field) {
            self::assertEmpty($responseArray[$field]);
        }
    }

    /**
     * @dataProvider validSearchDataProvider
     */
    public function test_valid_search_journey(array $queryParameters, array $expectedResponse): void
    {
        $queryString = !empty($queryParameters) ? '?' . http_build_query($queryParameters) : '';

        $response = $this->client->request('GET', '/car-make-model-search' . $queryString);
        $this->assertResponseCode($response, Response::HTTP_OK);

        self::assertSame($expectedResponse, $response->toArray());
    }

    private static function invalidSearchDataProvider(): iterable
    {
        yield 'No search parameters' => [
            [],
            ['makes'],
            ['models', 'years', 'vehicleData']
        ];

        yield 'Make provided' => [
            ['make' => 'POLESTAR'],
            ['makes', 'models'],
            ['years', 'vehicleData']
        ];

        yield 'Make & model provided' => [
            ['make' => 'POLESTAR', 'model' => 'POLESTAR 2'],
            ['makes', 'models', 'years'],
            ['vehicleData']
        ];

        yield 'Make, model & year provided' => [
            ['make' => 'POLESTAR', 'model' => 'POLESTAR 2', 'year' => 2021],
            ['makes', 'models', 'years', 'vehicleData'],
            []
        ];

        yield 'Only year provided' => [
            ['year' => 2021],
            ['makes'],
            ['models', 'years', 'vehicleData']
        ];

        yield 'Make & year provided' => [
            ['make' => 'POLESTAR', 'year' => 2021],
            ['makes', 'models'],
            ['years', 'vehicleData']
        ];

        yield 'Model & year provided' => [
            ['model' => 'POLESTAR 2', 'year' => 2021],
            ['makes'],
            ['models', 'years', 'vehicleData']
        ];

        yield 'Only model provided' => [
            ['model' => 'POLESTAR 2'],
            ['makes'],
            ['models', 'years', 'vehicleData']
        ];
    }

    private static function validSearchDataProvider(): iterable
    {
        yield 'Empty Search' => [
            [],
            [
                '@context' => '/contexts/CarMakeModelDetailsDto',
                '@id' => '/car-make-model-search',
                '@type' => 'CarMakeModelDetailsDto',
                'makes' => [
                    'MASERATI',
                    'POLESTAR'
                ],
                'models' => [],
                'years' => [],
                'vehicleData' => []
            ]
        ];

        yield 'Make provided' => [
            ['make' => 'POLESTAR'],
            [
                '@context' => '/contexts/CarMakeModelDetailsDto',
                '@id' => '/car-make-model-search',
                '@type' => 'CarMakeModelDetailsDto',
                'makes' => [
                    'MASERATI',
                    'POLESTAR'
                ],
                'models' => [
                    'POLESTAR 1',
                    'POLESTAR 2',
                    'POLESTAR 3',
                    'POLESTAR 4'
                ],
                'years' => [],
                'vehicleData' => []
            ]
        ];

        yield 'Make and Model provided' => [
            ['make' => 'POLESTAR', 'model' => 'POLESTAR 2'],
            [
                '@context' => '/contexts/CarMakeModelDetailsDto',
                '@id' => '/car-make-model-search',
                '@type' => 'CarMakeModelDetailsDto',
                'makes' => [
                    'MASERATI',
                    'POLESTAR'
                ],
                'models' => [
                    'POLESTAR 1',
                    'POLESTAR 2',
                    'POLESTAR 3',
                    'POLESTAR 4'
                ],
                'years' => [
                    2020,
                    2021,
                    2022,
                    2023
                ],
                'vehicleData' => []
            ]
        ];

        yield 'Make, model & year provided' => [
            ['make' => 'POLESTAR', 'model' => 'POLESTAR 2', 'year' => 2021],
            [
                '@context' => '/contexts/CarMakeModelDetailsDto',
                '@id' => '/car-make-model-search',
                '@type' => 'CarMakeModelDetailsDto',
                'makes' => [
                    'MASERATI',
                    'POLESTAR'
                ],
                'models' => [
                    'POLESTAR 1',
                    'POLESTAR 2',
                    'POLESTAR 3',
                    'POLESTAR 4'
                ],
                'years' => [
                    2020,
                    2021,
                    2022,
                    2023
                ],
                'vehicleData' => [
                    [
                        '@type' => 'VehicleData',
                        '@id' => '/vehicle-datas/3',
                        'id' => 3,
                        'kType' => 701872,
                        'marque' => 'POLESTAR',
                        'modelRange' => 'POLESTAR 2',
                        'introYear' => 2021,
                        'endYear' => null,
                        'bodyType' => 'SALOON',
                        'variant' => 'BASE'
                    ],
                    [
                        '@type' => 'VehicleData',
                        '@id' => '/vehicle-datas/5',
                        'id' => 5,
                        'kType' => 701874,
                        'marque' => 'POLESTAR',
                        'modelRange' => 'POLESTAR 2',
                        'introYear' => 2021,
                        'endYear' => null,
                        'bodyType' => 'SALOON',
                        'variant' => 'BASE'
                    ],
                    [
                        '@type' => 'VehicleData',
                        '@id' => '/vehicle-datas/8',
                        'id' => 8,
                        'kType' => 701877,
                        'marque' => 'POLESTAR',
                        'modelRange' => 'POLESTAR 2',
                        'introYear' => 2021,
                        'endYear' => null,
                        'bodyType' => 'SALOON',
                        'variant' => 'BASE'
                    ],
                    [
                        '@type' => 'VehicleData',
                        '@id' => '/vehicle-datas/4',
                        'id' => 4,
                        'kType' => 701873,
                        'marque' => 'POLESTAR',
                        'modelRange' => 'POLESTAR 2',
                        'introYear' => 2021,
                        'endYear' => null,
                        'bodyType' => 'SALOON',
                        'variant' => 'PLUS'
                    ],
                    [
                        '@type' => 'VehicleData',
                        '@id' => '/vehicle-datas/6',
                        'id' => 6,
                        'kType' => 701875,
                        'marque' => 'POLESTAR',
                        'modelRange' => 'POLESTAR 2',
                        'introYear' => 2021,
                        'endYear' => null,
                        'bodyType' => 'SALOON',
                        'variant' => 'PLUS'
                    ],
                    [
                        '@type' => 'VehicleData',
                        '@id' => '/vehicle-datas/7',
                        'id' => 7,
                        'kType' => 701876,
                        'marque' => 'POLESTAR',
                        'modelRange' => 'POLESTAR 2',
                        'introYear' => 2021,
                        'endYear' => null,
                        'bodyType' => 'SALOON',
                        'variant' => 'PLUS'
                    ]
                ]
            ]
        ];
    }
}
