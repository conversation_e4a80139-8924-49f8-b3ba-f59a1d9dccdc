<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use App\Entity\Product;
use App\Entity\ProductRefCompatibility;
use Doctrine\ORM\EntityManagerInterface;
use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class ProductRefCompatibilityTest extends OptimizedJsonApiTestCase
{
    public function test_deleting_product_cascades_to_ref_compatibilities(): void
    {
        $this->authenticateClient('<EMAIL>', 'engage');

        $productRepository = $this->getEntityManager()->getRepository(Product::class);
        $productRefCompatibilityRepository = $this->getEntityManager()->getRepository(ProductRefCompatibility::class);

        $product = $productRepository->findOneBy(['sku' => 'PK123456']);
        self::assertNotNull($product);

        $refCompatibilities = $productRefCompatibilityRepository
            ->findBy(['product' => $product]);

        self::assertNotEmpty($refCompatibilities);

        $productId = $product->getId();
        $refCompatibilityIds = array_map(static fn($ref) => $ref->getId(), $refCompatibilities);

        $response = $this->client->request('DELETE', '/products/' . $productId);
        $this->assertResponseCode($response, Response::HTTP_NO_CONTENT);

        $this->getEntityManager()->clear();

        $deletedProduct = $productRepository->find($productId);
        self::assertNull($deletedProduct);

        foreach ($refCompatibilityIds as $compatibilityId) {
            $deletedRefComp = $this->getEntityManager()->find(ProductRefCompatibility::class, $compatibilityId);
            self::assertNull($deletedRefComp, sprintf('Ref compatibility %d should be deleted', $compatibilityId));
        }
    }
}
