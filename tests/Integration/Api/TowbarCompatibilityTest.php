<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class TowbarCompatibilityTest extends OptimizedJsonApiTestCase
{
    /**
     * @dataProvider validKTypeDataProvider
     */
    public function test_get_towbar_compatibility_returns_complete_response(int $kType, string $expectedResponseFilename): void
    {
        $response = $this->client->request('GET', '/towbars/compatibility/' . $kType);

        $this->assertResponse($response, $expectedResponseFilename, Response::HTTP_OK);
    }

    public function test_get_towbar_compatibility_not_found(): void
    {
        $this->authenticateClient('<EMAIL>', 'engage');

        $response = $this->client->request('GET', '/towbars/compatibility/999999');

        $this->assertResponseCode($response, Response::HTTP_NOT_FOUND);
    }

    private static function validKTypeDataProvider(): iterable
    {
        yield 'K-Type with 3 products' => [
            690202,
            'towbar_compatibility/ref-id-690202-with-3-products',
        ];

        yield 'K-Type with 1 product' => [
            701873,
            'towbar_compatibility/ref-id-701873-with-1-product',
        ];
    }
}
