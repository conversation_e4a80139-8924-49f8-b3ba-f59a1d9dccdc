<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use App\DependencyInjection\Compiler\DataTransferPass;
use App\Entity\VehicleData;
use Peracto\Testing\TestCase\AbstractImportTest;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;

class VehicleDataImportTest extends AbstractImportTest
{
    private const FIXTURES_FILE_PATH = __DIR__ . '/../../DataFixtures/ImportCsv/vehicle_data/';

    /**
     * @dataProvider invalidPropertiesDataProvider
     */
    public function test_invalid_properties(string $filename): void
    {
        $this->performTestingOfInvalidProperties(
            DataTransferPass::IMPORT_TYPE_VEHICLE_DATA,
            $filename,
            self::FIXTURES_FILE_PATH,
            sprintf('import_csv/vehicle_data/%s', $filename),
        );
    }

    public static function invalidPropertiesDataProvider(): array
    {
        return [
            ['vehicle_data_missing_ref_id'],
            ['vehicle_data_missing_model_range'],
            ['vehicle_data_missing_body_type'],
            ['vehicle_data_missing_marque'],
            ['vehicle_data_missing_intro_year'],
            ['vehicle_data_missing_variant'],
        ];
    }

    public function test_valid_import(): void
    {
        $fileName = 'vehicle_data_successful.csv';
        $filePath = self::FIXTURES_FILE_PATH . $fileName;

        $response = $this->importRequest(
            ['importType' => DataTransferPass::IMPORT_TYPE_VEHICLE_DATA, 'sourceType' => 'csv'],
            ['file' => new UploadedFile($filePath, $fileName, 'text/csv')]
        );

        $this->assertResponse($response, 'import_csv/vehicle_data/successful_import', Response::HTTP_CREATED);

        $this->assertCount(3, $this->getEntityManager()
                ->getRepository(VehicleData::class)
                ->findBy(['kType' => ['123', '456', '789']])
        );
    }

    public function test_import_properties_match_csv_values(): void
    {
        $fileName = 'vehicle_data_successful.csv';
        $filePath = self::FIXTURES_FILE_PATH . $fileName;

        $response = $this->importRequest(
            ['importType' => DataTransferPass::IMPORT_TYPE_VEHICLE_DATA, 'sourceType' => 'csv'],
            ['file' => new UploadedFile($filePath, $fileName, 'text/csv')]
        );

        $this->assertResponse($response, 'import_csv/vehicle_data/successful_import', Response::HTTP_CREATED);

        $vehicleDataEntities = $this->getEntityManager()
            ->getRepository(VehicleData::class)
            ->findBy(['kType' => ['123', '456', '789']]);

        $this->assertCount(3, $vehicleDataEntities);

        $bmwVehicle = $this->getEntityManager()
            ->getRepository(VehicleData::class)
            ->findOneBy(['kType' => '123']);

        $this->assertEquals('BMW', $bmwVehicle->getMarque());
        $this->assertEquals('3 SERIES', $bmwVehicle->getModelRange());
        $this->assertEquals('SALOON', $bmwVehicle->getBodyType());
        $this->assertEquals(2017, $bmwVehicle->getIntroYear());
        $this->assertEquals(2018, $bmwVehicle->getEndYear());
        $this->assertEquals('330I M SPORT SHADOW EDITION', $bmwVehicle->getVariant());

        $mercedesVehicle = $this->getEntityManager()
            ->getRepository(VehicleData::class)
            ->findOneBy(['kType' => '456']);

        $this->assertEquals('MERCEDES', $mercedesVehicle->getMarque());
        $this->assertEquals('SPRINTER', $mercedesVehicle->getModelRange());
        $this->assertEquals('MINIBUS', $mercedesVehicle->getBodyType());
        $this->assertEquals(2000, $mercedesVehicle->getIntroYear());
        $this->assertNull($mercedesVehicle->getEndYear());
        $this->assertEquals('208 CDI SWB', $mercedesVehicle->getVariant());
    }

    public function test_valid_import_with_purge_option(): void
    {
        $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->delete(VehicleData::class, 'vd')
            ->getQuery()
            ->execute();

        $fileName = 'vehicle_data_successful.csv';
        $filePath = self::FIXTURES_FILE_PATH . $fileName;

        $this->importRequest(
            ['importType' => DataTransferPass::IMPORT_TYPE_VEHICLE_DATA, 'sourceType' => 'csv'],
            ['file' => new UploadedFile($filePath, $fileName, 'text/csv')]
        );

        $this->assertCount(3, $this->getEntityManager()
            ->getRepository(VehicleData::class)
            ->findAll()
        );

        // Subsequent import with purge option
        $fileName = 'vehicle_data_successful_2.csv';
        $filePath = self::FIXTURES_FILE_PATH . $fileName;

        $response = $this->importRequest(
            [
                'importType' => DataTransferPass::IMPORT_TYPE_VEHICLE_DATA,
                'sourceType' => 'csv',
                'options' => ['purgeExistingData' => true]
            ],
            ['file' => new UploadedFile($filePath, $fileName, 'text/csv')]
        );

        $this->assertResponse($response, 'import_csv/vehicle_data/successful_import_2', Response::HTTP_CREATED);

        $this->assertCount(1, $this->getEntityManager()
            ->getRepository(VehicleData::class)
            ->findAll()
        );
    }
}