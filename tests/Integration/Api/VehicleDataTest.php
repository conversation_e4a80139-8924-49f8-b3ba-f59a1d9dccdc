<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class VehicleDataTest extends OptimizedJsonApiTestCase
{
    public function test_get_vehicle_data_collection_as_guest(): void
    {
        $this->authenticateClient(null, null);
        $response = $this->client->request('GET', '/vehicle-data');
        $this->assertResponseCode($response, Response::HTTP_FORBIDDEN);
    }

    public function test_get_vehicle_data_collection_as_user(): void
    {
        $this->authenticateClient('<EMAIL>', 'engage');

        $response = $this->client->request('GET', '/vehicle-data');
        $this->assertResponseCode($response, Response::HTTP_FORBIDDEN);
    }

    public function test_get_vehicle_data_collection_as_admin(): void
    {
        $this->authenticateClient('<EMAIL>', 'engage');

        $response = $this->client->request('GET', '/vehicle-data');
        $this->assertResponseCode($response, Response::HTTP_OK);

        $responseAsArray = $this->getJsonResponseAsArray($response);
        self::assertSame(205, $responseAsArray['hydra:totalItems']);
        $this->assertCount(30, $responseAsArray['hydra:member']);

        foreach ($responseAsArray['hydra:member'] as $vehicleData) {
            $this->assertArrayHasKey('id', $vehicleData);
            $this->assertArrayHasKey('kType', $vehicleData);
            $this->assertArrayHasKey('marque', $vehicleData);
            $this->assertArrayHasKey('modelRange', $vehicleData);
            $this->assertArrayHasKey('introYear', $vehicleData);
            $this->assertArrayHasKey('endYear', $vehicleData);
            $this->assertArrayHasKey('variant', $vehicleData);
            $this->assertArrayHasKey('bodyType', $vehicleData);
        }
    }
}
