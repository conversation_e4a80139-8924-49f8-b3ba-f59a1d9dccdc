<?php

declare(strict_types=1);

namespace App\Tests\Integration\Task;

use App\Entity\VehicleData;
use App\Repository\VehicleDataRepositoryInterface;
use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Peracto\Testing\TestCase\TaskTrait;

class VehicleDataImportTaskTest extends OptimizedJsonApiTestCase
{
    use TaskTrait;

    private ?VehicleDataRepositoryInterface $vehicleDataRepository;

    public function setUp(): void
    {
        parent::setUp();

        $this->vehicleDataRepository = $this->getContainer()->get(VehicleDataRepositoryInterface::class);

        $this->reduceVehicleDataFixtures();
    }

    public function test_import(): void
    {
        $this->pre_import_assertions();

        $this->executeTask('vehicle_data_importer');

        $this->getEntityManager()->clear();

        $this->post_import_assertions();
    }

    private function pre_import_assertions(): void
    {
        $vehicleData = $this->vehicleDataRepository->findAll();
        self::assertCount(2, $vehicleData);
        $this->assertExistingVehicleDataFixturesAreCorrect();
    }

    private function post_import_assertions(): void
    {
        $this->assertExistingVehicleDataFixturesAreCorrect();

        $vehicleData = $this->vehicleDataRepository->findAll();
        self::assertCount(5, $vehicleData);

        [, , $vehicleData3, $vehicleData4, $vehicleData5] = $vehicleData;

        self::assertSame(717855, $vehicleData3->getRefId());
        self::assertSame('TESLA', $vehicleData3->getMarque());
        self::assertSame('MODEL Y', $vehicleData3->getModelRange());
        self::assertSame(2024, $vehicleData3->getIntroYear());
        self::assertNull($vehicleData3->getEndYear());
        self::assertSame('MPV', $vehicleData3->getBodyType());
        self::assertSame('BASE', $vehicleData3->getVariant());

        self::assertSame(717856, $vehicleData4->getRefId());
        self::assertSame('TESLA', $vehicleData4->getMarque());
        self::assertSame('MODEL Y', $vehicleData4->getModelRange());
        self::assertSame(2024, $vehicleData4->getIntroYear());
        self::assertNull($vehicleData4->getEndYear());
        self::assertSame('MPV', $vehicleData4->getBodyType());
        self::assertSame('LONG RANGE AWD', $vehicleData4->getVariant());

        self::assertSame(717859, $vehicleData5->getRefId());
        self::assertSame('LAND ROVER', $vehicleData5->getMarque());
        self::assertSame('RANGE ROVER', $vehicleData5->getModelRange());
        self::assertSame(2010, $vehicleData5->getIntroYear());
        self::assertSame(2020, $vehicleData5->getEndYear());
        self::assertSame('ESTATE', $vehicleData5->getBodyType());
        self::assertSame('SE', $vehicleData5->getVariant());
    }

    private function assertExistingVehicleDataFixturesAreCorrect(): void
    {
        /** @var VehicleData[] $vehicleData */
        $vehicleData = $this->vehicleDataRepository->findAll();

        [$vehicleData1, $vehicleData2] = $vehicleData;

        self::assertSame(690202, $vehicleData1->getRefId());
        self::assertSame('POLESTAR', $vehicleData1->getMarque());
        self::assertSame('POLESTAR 1', $vehicleData1->getModelRange());
        self::assertSame(2020, $vehicleData1->getIntroYear());
        self::assertNull($vehicleData1->getEndYear());
        self::assertSame('COUPE', $vehicleData1->getBodyType());
        self::assertSame('BASE', $vehicleData1->getVariant());

        self::assertSame(692757, $vehicleData2->getKType());
        self::assertSame('POLESTAR', $vehicleData2->getMarque());
        self::assertSame('POLESTAR 2', $vehicleData2->getModelRange());
        self::assertSame(2020, $vehicleData2->getIntroYear());
        self::assertSame(2021, $vehicleData2->getEndYear());
        self::assertSame('SALOON', $vehicleData2->getBodyType());
        self::assertSame('PILOT PLUS', $vehicleData2->getVariant());
    }

    private function reduceVehicleDataFixtures(): void
    {
        $refIdsToKeep = [690202, 692757];

        $this->getEntityManager()
            ->createQueryBuilder()
            ->delete(VehicleData::class, 'vehicleData')
            ->where('vehicleData.kType NOT IN (:kTypes)')
            ->setParameter('kTypes', $refIdsToKeep)
            ->getQuery()
            ->execute();

        $this->getEntityManager()->clear();
    }
}
