<?php

declare(strict_types=1);

namespace App\Tests\Unit\Component\Carweb\Api\GetVehicleByVRM;

use App\Component\Carweb\Api\GetVehicleByVRM\Validator\GetVehicleByVRMResponseConstraint;
use App\Component\Carweb\Api\GetVehicleByVRM\Validator\GetVehicleByVRMResponseConstraintValidator;
use App\Component\Carweb\Api\PropertyHelper;
use App\Component\Carweb\Response\CarwebResponse;
use Peracto\Testing\TestCase\OptimizedConstraintValidatorTestCase;
use Symfony\Component\Validator\Constraints\Blank;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;

class GetVehicleByVRMResponseConstraintValidatorTest extends OptimizedConstraintValidatorTestCase
{
    protected function createValidator(): GetVehicleByVRMResponseConstraintValidator
    {
        return new GetVehicleByVRMResponseConstraintValidator();
    }

    public function test_exception_thrown_on_invalid_constraint(): void
    {
        $this->expectException(UnexpectedTypeException::class);

        $this->validator->validate(
            new CarwebResponse([]),
            new Blank()
        );
    }

    public function test_exception_thrown_on_invalid_value(): void
    {
        $this->expectException(UnexpectedValueException::class);

        $this->validator->validate(
            [],
            new GetVehicleByVRMResponseConstraint()
        );
    }

    /**
     * @dataProvider validationDataProvider
     */
    public function test_validation(array $value, ?array $violationFields): void
    {
        $response = new CarwebResponse($value);
        $constraint = new GetVehicleByVRMResponseConstraint();

        $this->validator->validate($response, $constraint);

        $assertion = null;

        if ($violationFields) {
            $first = true;

            foreach ($violationFields as $field) {
                if ($first) {
                    $first = false;

                    $assertion = $this
                        ->buildViolation($constraint->message)
                        ->setParameter('{{ value }}', $field);

                    continue;
                }

                $assertion = $assertion
                    ->buildNextViolation($constraint->message)
                    ->setParameter('{{ value }}', $field);
            }

            $assertion->assertRaised();
        } else {
            self::assertNoViolation();
        }
    }

    public static function validationDataProvider(): iterable
    {
        yield 'Missing DataArea:Vehicles:Vehicle field' => [
            'value' => [],
            'violationFields' => ['DataArea:Vehicles:Vehicle'],
        ];

        yield 'All required fields missing' => [
            'value' => [
                'DataArea' => [
                    'Vehicles' => [
                        'Vehicle' => []
                    ]
                ]
            ],
            'violationFields' => PropertyHelper::CARWEB_GET_VEHICLE_BY_VRM_RESPONSE_REQUIRED_FIELDS
        ];

        yield 'Some required fields missing' => [
            'value' => [
                'DataArea' => [
                    'Vehicles' => [
                        'Vehicle' => [
                            PropertyHelper::CARWEB_FIELD_MODEL => 'Test model',
                            PropertyHelper::CARWEB_FIELD_MAKE => 'Test make'
                        ]
                    ]
                ]
            ],
            'violationFields' => [
                PropertyHelper::CARWEB_FIELD_DATE_FIRST_REGISTERED,
                PropertyHelper::CARWEB_FIELD_BODY_STYLE,
                PropertyHelper::CARWEB_FIELD_DESCRIPTION,
                PropertyHelper::CARWEB_FIELD_K_TYPE,
            ]
        ];

        yield 'All required fields present' => [
            'value' => [
                'DataArea' => [
                    'Vehicles' => [
                        'Vehicle' => [
                            PropertyHelper::CARWEB_FIELD_MAKE => 'Test make',
                            PropertyHelper::CARWEB_FIELD_MODEL => 'Test model',
                            PropertyHelper::CARWEB_FIELD_DATE_FIRST_REGISTERED => 'Test date',
                            PropertyHelper::CARWEB_FIELD_BODY_STYLE => 'Test body style',
                            PropertyHelper::CARWEB_FIELD_DESCRIPTION => 'Test description',
                            PropertyHelper::CARWEB_FIELD_REF_ID => '123456',
                            PropertyHelper::CARWEB_FIELD_K_TYPE => '654321',
                        ]
                    ]
                ]
            ],
            'violationFields' => null
        ];

        yield 'Validation should skip when dealing with an error response' => [
            'value' => [
                'DataArea' => [
                    'Error' => ['Details' => [
                        'ErrorCode' => 1000,
                        'ErrorDescription' => 'No Vehicles Returned',
                    ]]
                ]
            ],
            'violationFields' => null
        ];
    }
}
