<?php

declare(strict_types=1);

namespace App\Tests\Unit\Component\Carweb\Api\GetVehicleByVRM;

use App\Component\Carweb\Api\GetVehicleByVRM\GetVehicleByVRMService;
use App\Component\Carweb\Api\GetVehicleByVRM\Request\GetVehicleByVRMRequest;
use App\Component\Carweb\Api\PropertyHelper;
use App\Component\Carweb\CarwebHttpClientInterface;
use App\Component\Carweb\Response\CarwebResponseInterface;
use App\Component\Carweb\Validator\CarwebRequestValidatorInterface;
use App\Component\Carweb\Validator\CarwebResponseValidatorInterface;
use Peracto\Testing\TestCase\OptimizedPHPUnitTestCase;
use Prophecy\Argument;
use Prophecy\Prophecy\ObjectProphecy;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class GetVehicleByVRMServiceTest extends OptimizedPHPUnitTestCase
{
    private GetVehicleByVRMService $sut;

    private CarwebHttpClientInterface|ObjectProphecy $client;

    protected function setUp(): void
    {
        $this->client = $this->prophesize(CarwebHttpClientInterface::class);
        $requestValidator = $this->prophesize(CarwebRequestValidatorInterface::class);
        $responseValidator = $this->prophesize(CarwebResponseValidatorInterface::class);

        $violations = $this->prophesize(ConstraintViolationListInterface::class);
        $violations->count()->willReturn(0);

        $this->sut = new GetVehicleByVRMService(
            $this->client->reveal(),
            $requestValidator->reveal(),
            $responseValidator->reveal(),
        );
    }

    public function test_service_returns_expected_vehicle_details(): void
    {
        $carwebResponse = $this->prophesize(CarwebResponseInterface::class);
        $carwebResponse->getResponseData()->willReturn([
            'DataArea' => ['Vehicles' => ['Vehicle' => [
                PropertyHelper::CARWEB_FIELD_MAKE => 'Test make',
                PropertyHelper::CARWEB_FIELD_MODEL => 'Test model',
                PropertyHelper::CARWEB_FIELD_DATE_FIRST_REGISTERED => '2024-05-01',
                PropertyHelper::CARWEB_FIELD_MODEL_SERIES => 'Test series',
                PropertyHelper::CARWEB_FIELD_BODY_STYLE => 'Test body style',
                PropertyHelper::CARWEB_FIELD_DESCRIPTION => 'Test description',
                PropertyHelper::CARWEB_FIELD_K_TYPE => '234234234'
        ]]]]);
        $carwebResponse->getRequest()->willReturn(null);

        $this
            ->client
            ->request(Argument::type(GetVehicleByVRMRequest::class))
            ->willReturn($carwebResponse->reveal());

        $getVehicleByVRMResponse = $this->sut->getVehicleDetailsByVrm('testvrm');

        self::assertTrue($getVehicleByVRMResponse->isSuccess());
        self::assertEquals([
            'make' => 'Test make',
            'model' => 'Test model',
            'year' => 2024,
            'dateFirstRegistered' => '2024-05-01',
            'modelSeries' => 'Test series',
            'bodyStyle' => 'Test body style',
            'description' => 'Test description',
            'kType' => '234234234'
        ], $getVehicleByVRMResponse->getVehicleDetails());
        self::assertNull($getVehicleByVRMResponse->getErrorCode());
        self::assertNull($getVehicleByVRMResponse->getErrorDescription());
    }

    public function test_service_returns_nothing_when_no_vehicle_is_found(): void
    {
        $carwebResponse = $this->prophesize(CarwebResponseInterface::class);
        $carwebResponse->getResponseData()->willReturn([
            'DataArea' => ['Error' => ['Details' => [
                'ErrorCode' => 1000,
                'ErrorDescription' => 'No Vehicles Returned',
            ]]]]);
        $carwebResponse->getRequest()->willReturn(null);

        $this
            ->client
            ->request(Argument::type(GetVehicleByVRMRequest::class))
            ->willReturn($carwebResponse->reveal());

        $getVehicleByVRMResponse = $this->sut->getVehicleDetailsByVrm('testvrm');

        self::assertFalse($getVehicleByVRMResponse->isSuccess());
        self::assertNull($getVehicleByVRMResponse->getVehicleDetails());
        self::assertEquals($getVehicleByVRMResponse->getErrorCode(), 1000);
        self::assertEquals($getVehicleByVRMResponse->getErrorDescription(), 'No Vehicles Returned');
    }
}
