<?php

declare(strict_types=1);

namespace App\Tests\Unit\Component\VehicleData;

use App\Component\File\Exception\ImportVehicleDataException;
use App\Component\File\FileStorageServiceInterface;
use App\Component\VehicleData\VehicleDataService;
use Carbon\Carbon;
use Peracto\Testing\TestCase\OptimizedPHPUnitTestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class VehicleDataServiceTest extends OptimizedPHPUnitTestCase
{
    protected function setUp(): void
    {
        Carbon::setTestNow(Carbon::create(2025, 1, 1, 12, 30, 45));
    }

    public function test_exception_is_raised_when_column_is_missing_from_file(): void
    {
        $this->expectException(ImportVehicleDataException::class);

        $fixtureFullPath = realpath(DATA_FIXTURES_ROOT . '/VehicleData/missing_maque_column.csv');
        $logger = $this->prophesize(LoggerInterface::class);
        $fileStorageService = $this->prophesize(FileStorageServiceInterface::class);
        $fileStorageService->downloadFile('vehicle_data/New_Codes.csv')
            ->shouldBeCalledOnce();
        $fileStorageService->moveFile('vehicle_data/New_Codes.csv', 'vehicle_data/processed/processed_vehicle_data_2025_01_01_123045.csv')
            ->shouldBeCalledOnce();
        $fileStorageService->getDownloadedFileFullPath('vehicle_data/New_Codes.csv')->willReturn($fixtureFullPath);

        $vehicleDataService = new VehicleDataService($fileStorageService->reveal());
        $vehicleDataService->setLogger($logger->reveal());

        $vehicleDataService->getVehicleData();

        $logger->alert('<error>Missing the following columns: MARQUE</error>')
            ->shouldBeCalledOnce();
    }

    public function test_exception_is_raised_when_file_not_found(): void
    {
        $this->expectException(ImportVehicleDataException::class);
        $this->expectExceptionMessage('<error>Unable to locate the source file. Error: The file "" does not exist</error>');

        $logger = $this->prophesize(LoggerInterface::class);
        $fileStorageService = $this->prophesize(FileStorageServiceInterface::class);
        $fileStorageService->downloadFile('vehicle_data/New_Codes.csv')
            ->shouldBeCalledOnce();
        $fileStorageService->moveFile('vehicle_data/New_Codes.csv', 'vehicle_data/processed/processed_vehicle_data_2025_01_01_123045.csv')
            ->shouldBeCalledOnce();
        $fileStorageService->getDownloadedFileFullPath('vehicle_data/New_Codes.csv')->willReturn('');

        $vehicleDataService = new VehicleDataService($fileStorageService->reveal());
        $vehicleDataService->setLogger($logger->reveal());

        $vehicleDataService->getVehicleData();
    }

    public function test_vehicle_data_is_returned(): void
    {
        $fixtureFullPath = realpath(DATA_FIXTURES_ROOT . '/VehicleData/multiple_vehicles.csv');
        $logger = $this->prophesize(LoggerInterface::class);
        $fileStorageService = $this->prophesize(FileStorageServiceInterface::class);
        $fileStorageService->downloadFile('vehicle_data/New_Codes.csv')
            ->shouldBeCalledOnce();
        $fileStorageService->moveFile('vehicle_data/New_Codes.csv', 'vehicle_data/processed/processed_vehicle_data_2025_01_01_123045.csv')
            ->shouldBeCalledOnce();
        $fileStorageService->getDownloadedFileFullPath('vehicle_data/New_Codes.csv')->willReturn($fixtureFullPath);

        $vehicleDataService = new VehicleDataService($fileStorageService->reveal());
        $vehicleDataService->setLogger($logger->reveal());

        self::assertSame([
            'headers' => [
                'K TYPE',
                'VEHICLE CATEGORY DESCRIPTION',
                'MARQUE',
                'MODEL RANGE',
                'INTRO YEAR',
                'END YEAR',
                'DOORS',
                'BODY TYPE',
                'RANGE SERIES',
                'VARIANT'
            ],
            'data' => [
                [
                    '717855',
                    'CARS (exc. Off-Road)',
                    'TESLA',
                    'MODEL Y',
                    '2024',
                    '',
                    '5',
                    'MPV',
                    '1',
                    'BASE'
                ],
                [
                    '717856',
                    'CARS (exc. Off-Road)',
                    'TESLA',
                    'MODEL Y',
                    '2024',
                    '',
                    '5',
                    'MPV',
                    '1',
                    'LONG RANGE AWD'
                ],
                [
                    '717859',
                    'CARS (exc. Off-Road)',
                    'LAND ROVER',
                    'RANGE ROVER',
                    '2010',
                    '2020',
                    '5',
                    'ESTATE',
                    'L460',
                    'SE'
                ]
            ]
        ], $vehicleDataService->getVehicleData());
    }
}
